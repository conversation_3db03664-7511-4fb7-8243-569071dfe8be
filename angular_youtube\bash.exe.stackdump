Stack trace:
Frame         Function      Args
0007FFFFBB40  00021006118E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFFAA40) msys-2.0.dll+0x2118E
0007FFFFBB40  0002100469BA (000000000000, 000000000000, 000000000000, 000000000004) msys-2.0.dll+0x69BA
0007FFFFBB40  0002100469F2 (00021028DF99, 0007FFFFB9F8, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFFBB40  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFFBB40  00021006A545 (0007FFFFBB50, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0001004F94B7  00021006B9A5 (0007FFFFBB50, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF985720000 ntdll.dll
7FF984390000 KERNEL32.DLL
7FF982A30000 KERNELBASE.dll
7FF984500000 USER32.dll
7FF982EE0000 win32u.dll
000210040000 msys-2.0.dll
7FF9844D0000 GDI32.dll
7FF983150000 gdi32full.dll
7FF982E30000 msvcp_win.dll
7FF9828E0000 ucrtbase.dll
7FF984760000 advapi32.dll
7FF983600000 msvcrt.dll
7FF983860000 sechost.dll
7FF985540000 RPCRT4.dll
7FF981D40000 CRYPTBASE.DLL
7FF983410000 bcryptPrimitives.dll
7FF984720000 IMM32.DLL
