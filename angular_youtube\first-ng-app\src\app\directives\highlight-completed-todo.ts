import { Directive,input,effect, ElementRef, inject } from '@angular/core';

@Directive({
  selector: '[appHighlightCompletedTodo]'
})
export class HighlightCompletedTodo {
  isCompleted = input(false)
  el = inject(ElementRef)
  constructor() { }
  styleEffect = effect(() => {
    if(this.isCompleted()){
      this.el.nativeElement.style.textDecoration = 'line-through';
      this.el.nativeElement.style.color = 'red';
      this.el.nativeElement.style.opacity = '0.5';
    } else {
      this.el.nativeElement.style.textDecoration = 'none';
      this.el.nativeElement.style.color = 'black';
      this.el.nativeElement.style.opacity = '1';
    }
  })
}
