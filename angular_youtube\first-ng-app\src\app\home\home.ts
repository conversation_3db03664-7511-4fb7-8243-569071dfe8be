import { Component, signal } from '@angular/core';
import { Greeting } from "../components/greeting/greeting";

@Component({
  selector: 'app-home',
  imports: [Greeting],
  templateUrl: './home.html',
  styleUrls: ['./home.scss'],
})
export class Home {
  message = signal('hello from greetinging home');
  inputValue = signal('');
  keyupHandler(event: KeyboardEvent) {
    this.message.set(event.key);
  }
}
