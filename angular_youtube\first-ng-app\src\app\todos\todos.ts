import { Component,inject, OnInit, signal } from '@angular/core';
import { Todos as TodosService } from '../services/todos';
import { Todo } from '../model/todo.type';
import { TodoItem } from '../components/todo-item/todo-item';
import { catchError } from 'rxjs';
import { FormsModule } from '@angular/forms';
import { FilterTodosPipe } from '../pipes/filter-todos-pipe';
@Component({
  selector: 'app-todos',
  imports: [TodoItem,FormsModule,FilterTodosPipe],
  templateUrl: './todos.html',
  styleUrl: './todos.scss',
})
export class Todos implements OnInit {
  todoService = inject(TodosService);
  todoItems = signal<Array<Todo>>([])
  searchTerm = signal('')
  ngOnInit() {
    this.todoService.getTodosFromApi().pipe( catchError((err) => {
      console.log(err);
      return [];
    })).subscribe(todos => {
      this.todoItems.set(todos);
    })
  }
  updateTodoItem(todoItem:Todo) {
     this.todoItems.update(todos => todos.map(todo => {
      if(todo.id === todoItem.id) {
        return {...todoItem,completed: !todo.completed};
      }
      return todo;
    }))
  }
}
