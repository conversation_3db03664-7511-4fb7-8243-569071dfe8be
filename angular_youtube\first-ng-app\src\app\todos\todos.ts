import { Component,inject, OnInit, signal } from '@angular/core';
import { Todos as TodosService } from '../services/todos';
import { Todo } from '../model/todo.type';
import { TodoItem } from '../components/todo-item/todo-item';
import { catchError } from 'rxjs';
@Component({
  selector: 'app-todos',
  imports: [TodoItem],
  templateUrl: './todos.html',
  styleUrl: './todos.scss',
})
export class Todos implements OnInit {
  todoService = inject(TodosService);
  todoItems = signal<Array<Todo>>([])
  ngOnInit() {
    this.todoService.getTodosFromApi().pipe( catchError((err) => {
      console.log(err);
      return [];
    })).subscribe(todos => {
      this.todoItems.set(todos);
    })
  }
}
