import { Component } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { Header } from './components/header/header';
import { Home } from './home/<USER>';
import { About } from './components/about/about';

@Component({
  selector: 'app-root',
  imports: [RouterOutlet, Home, Header, About],
  template: `
    <app-header />
    <main>
      <app-about />
      <app-home />
      <router-outlet />
    </main>
  `,
  styles: [
    `
      h1 {
        color: red;
      }
    `,
    `
      p {
        color: blue;
      }
    `,
  ],
})
export class App {}
