{
  "$schema": "vscode://schemas/color-theme",
  "type": "dark",
  "colors": {
    "actionBar.toggledBackground": "#383a49",
    "activityBar.activeBorder": "#0078d4",
    "activityBar.background": "#181818",
    "activityBar.border": "#2b2b2b",
    "activityBar.foreground": "#d7d7d7",
    "activityBar.inactiveForeground": "#868686",
    "activityBarBadge.background": "#0078d4",
    "activityBarBadge.foreground": "#ffffff",
    "badge.background": "#616161",
    "badge.foreground": "#f8f8f8",
    "button.background": "#0078d4",
    "button.border": "#ffffff12",
    "button.foreground": "#ffffff",
    "button.hoverBackground": "#026ec1",
    "button.secondaryBackground": "#313131",
    "button.secondaryForeground": "#cccccc",
    "button.secondaryHoverBackground": "#3c3c3c",
    "chat.editedFileForeground": "#e2c08d",
    "chat.slashCommandBackground": "#26477866",
    "chat.slashCommandForeground": "#85b6ff",
    "checkbox.background": "#313131",
    "checkbox.border": "#3c3c3c",
    "debugToolBar.background": "#181818",
    "descriptionForeground": "#9d9d9d",
    "dropdown.background": "#313131",
    "dropdown.border": "#3c3c3c",
    "dropdown.foreground": "#cccccc",
    "dropdown.listBackground": "#1f1f1f",
    "editor.background": "#1f1f1f",
    "editor.findMatchBackground": "#9e6a03",
    "editor.foreground": "#cccccc",
    "editor.inactiveSelectionBackground": "#3a3d41",
    "editor.selectionHighlightBackground": "#add6ff26",
    "editorGroup.border": "#ffffff17",
    "editorGroupHeader.tabsBackground": "#181818",
    "editorGroupHeader.tabsBorder": "#2b2b2b",
    "editorGutter.addedBackground": "#2ea043",
    "editorGutter.deletedBackground": "#f85149",
    "editorGutter.modifiedBackground": "#0078d4",
    "editorIndentGuide.activeBackground1": "#707070",
    "editorIndentGuide.background1": "#404040",
    "editorLineNumber.activeForeground": "#cccccc",
    "editorLineNumber.foreground": "#6e7681",
    "editorOverviewRuler.border": "#010409",
    "editorWidget.background": "#202020",
    "errorForeground": "#f85149",
    "focusBorder": "#0078d4",
    "foreground": "#cccccc",
    "icon.foreground": "#cccccc",
    "input.background": "#313131",
    "input.border": "#3c3c3c",
    "input.foreground": "#cccccc",
    "input.placeholderForeground": "#989898",
    "inputOption.activeBackground": "#2489db82",
    "inputOption.activeBorder": "#2488db",
    "keybindingLabel.foreground": "#cccccc",
    "list.activeSelectionIconForeground": "#ffffff",
    "list.dropBackground": "#383b3d",
    "menu.background": "#1f1f1f",
    "menu.border": "#454545",
    "menu.foreground": "#cccccc",
    "menu.selectionBackground": "#0078d4",
    "menu.separatorBackground": "#454545",
    "notificationCenterHeader.background": "#1f1f1f",
    "notificationCenterHeader.foreground": "#cccccc",
    "notifications.background": "#1f1f1f",
    "notifications.border": "#2b2b2b",
    "notifications.foreground": "#cccccc",
    "panel.background": "#181818",
    "panel.border": "#2b2b2b",
    "panelInput.border": "#2b2b2b",
    "panelTitle.activeBorder": "#0078d4",
    "panelTitle.activeForeground": "#cccccc",
    "panelTitle.inactiveForeground": "#9d9d9d",
    "peekViewEditor.background": "#1f1f1f",
    "peekViewEditor.matchHighlightBackground": "#bb800966",
    "peekViewResult.background": "#1f1f1f",
    "peekViewResult.matchHighlightBackground": "#bb800966",
    "pickerGroup.border": "#3c3c3c",
    "ports.iconRunningProcessForeground": "#369432",
    "progressBar.background": "#0078d4",
    "quickInput.background": "#222222",
    "quickInput.foreground": "#cccccc",
    "settings.dropdownBackground": "#313131",
    "settings.dropdownBorder": "#3c3c3c",
    "settings.headerForeground": "#ffffff",
    "settings.modifiedItemIndicator": "#bb800966",
    "sideBar.background": "#181818",
    "sideBar.border": "#2b2b2b",
    "sideBar.foreground": "#cccccc",
    "sideBarSectionHeader.background": "#181818",
    "sideBarSectionHeader.border": "#2b2b2b",
    "sideBarSectionHeader.foreground": "#cccccc",
    "sideBarTitle.foreground": "#cccccc",
    "statusBar.background": "#181818",
    "statusBar.border": "#2b2b2b",
    "statusBar.debuggingBackground": "#0078d4",
    "statusBar.debuggingForeground": "#ffffff",
    "statusBar.focusBorder": "#0078d4",
    "statusBar.foreground": "#cccccc",
    "statusBar.noFolderBackground": "#1f1f1f",
    "statusBarItem.focusBorder": "#0078d4",
    "statusBarItem.prominentBackground": "#6e768166",
    "statusBarItem.remoteBackground": "#0078d4",
    "statusBarItem.remoteForeground": "#ffffff",
    "tab.activeBackground": "#1f1f1f",
    "tab.activeBorder": "#1f1f1f",
    "tab.activeBorderTop": "#0078d4",
    "tab.activeForeground": "#ffffff",
    "tab.border": "#2b2b2b",
    "tab.hoverBackground": "#1f1f1f",
    "tab.inactiveBackground": "#181818",
    "tab.inactiveForeground": "#9d9d9d",
    "tab.lastPinnedBorder": "#cccccc33",
    "tab.selectedBackground": "#222222",
    "tab.selectedBorderTop": "#6caddf",
    "tab.selectedForeground": "#ffffffa0",
    "tab.unfocusedActiveBorder": "#1f1f1f",
    "tab.unfocusedActiveBorderTop": "#2b2b2b",
    "tab.unfocusedHoverBackground": "#1f1f1f",
    "terminal.foreground": "#cccccc",
    "terminal.inactiveSelectionBackground": "#3a3d41",
    "terminal.tab.activeBorder": "#0078d4",
    "textBlockQuote.background": "#2b2b2b",
    "textBlockQuote.border": "#616161",
    "textCodeBlock.background": "#2b2b2b",
    "textLink.activeForeground": "#4daafc",
    "textLink.foreground": "#4daafc",
    "textPreformat.background": "#3c3c3c",
    "textPreformat.foreground": "#d0d0d0",
    "textSeparator.foreground": "#21262d",
    "titleBar.activeBackground": "#181818",
    "titleBar.activeForeground": "#cccccc",
    "titleBar.border": "#2b2b2b",
    "titleBar.inactiveBackground": "#1f1f1f",
    "titleBar.inactiveForeground": "#9d9d9d",
    "welcomePage.progress.foreground": "#0078d4",
    "welcomePage.tileBackground": "#2b2b2b",
    "widget.border": "#313131"
    //"activityBar.dropBorder": "#d7d7d7",
    //"activityBarTop.activeBorder": "#e7e7e7",
    //"activityBarTop.dropBorder": "#e7e7e7",
    //"activityBarTop.foreground": "#e7e7e7",
    //"activityBarTop.inactiveForeground": "#e7e7e799",
    //"activityErrorBadge.background": "#f14c4c",
    //"activityErrorBadge.foreground": "#000000",
    //"activityWarningBadge.background": "#cca700",
    //"activityWarningBadge.foreground": "#000000",
    //"banner.background": "#04395e",
    //"banner.foreground": "#ffffff",
    //"banner.iconForeground": "#3794ff",
    //"breadcrumb.activeSelectionForeground": "#e0e0e0",
    //"breadcrumb.background": "#1f1f1f",
    //"breadcrumb.focusForeground": "#e0e0e0",
    //"breadcrumb.foreground": "#cccccccc",
    //"breadcrumbPicker.background": "#202020",
    //"button.separator": "#ffffff66",
    //"chart.axis": "#bfbfbf66",
    //"chart.guide": "#bfbfbf33",
    //"chart.line": "#236b8e",
    //"charts.blue": "#3794ff",
    //"charts.foreground": "#cccccc",
    //"charts.green": "#89d185",
    //"charts.lines": "#cccccc80",
    //"charts.orange": "#d18616",
    //"charts.purple": "#b180d7",
    //"charts.red": "#f14c4c",
    //"charts.yellow": "#cca700",
    //"chat.avatarBackground": "#1f1f1f",
    //"chat.avatarForeground": "#cccccc",
    //"chat.checkpointSeparator": "#585858",
    //"chat.linesAddedForeground": "#54b054",
    //"chat.linesRemovedForeground": "#fc6a6a",
    //"chat.requestBackground": "#1f1f1f9e",
    //"chat.requestBorder": "#ffffff1a",
    //"chat.requestBubbleBackground": "#264f784d",
    //"chat.requestBubbleHoverBackground": "#264f78",
    //"chat.requestCodeBorder": "#004972b8",
    //"checkbox.disabled.background": "#646464",
    //"checkbox.disabled.foreground": "#989898",
    //"checkbox.foreground": "#cccccc",
    //"checkbox.selectBackground": "#202020",
    //"checkbox.selectBorder": "#cccccc",
    //"commandCenter.activeBackground": "#ffffff14",
    //"commandCenter.activeBorder": "#cccccc4d",
    //"commandCenter.activeForeground": "#cccccc",
    //"commandCenter.background": "#ffffff0d",
    //"commandCenter.border": "#cccccc33",
    //"commandCenter.debuggingBackground": "#0078d442",
    //"commandCenter.foreground": "#cccccc",
    //"commandCenter.inactiveBorder": "#9d9d9d40",
    //"commandCenter.inactiveForeground": "#9d9d9d",
    //"commentsView.resolvedIcon": "#cccccc80",
    //"commentsView.unresolvedIcon": "#0078d4",
    //"debugConsole.errorForeground": "#f85149",
    //"debugConsole.infoForeground": "#3794ff",
    //"debugConsole.sourceForeground": "#cccccc",
    //"debugConsole.warningForeground": "#cca700",
    //"debugConsoleInputIcon.foreground": "#cccccc",
    //"debugExceptionWidget.background": "#420b0d",
    //"debugExceptionWidget.border": "#a31515",
    //"debugIcon.breakpointCurrentStackframeForeground": "#ffcc00",
    //"debugIcon.breakpointDisabledForeground": "#848484",
    //"debugIcon.breakpointForeground": "#e51400",
    //"debugIcon.breakpointStackframeForeground": "#89d185",
    //"debugIcon.breakpointUnverifiedForeground": "#848484",
    //"debugIcon.continueForeground": "#75beff",
    //"debugIcon.disconnectForeground": "#f48771",
    //"debugIcon.pauseForeground": "#75beff",
    //"debugIcon.restartForeground": "#89d185",
    //"debugIcon.startForeground": "#89d185",
    //"debugIcon.stepBackForeground": "#75beff",
    //"debugIcon.stepIntoForeground": "#75beff",
    //"debugIcon.stepOutForeground": "#75beff",
    //"debugIcon.stepOverForeground": "#75beff",
    //"debugIcon.stopForeground": "#f48771",
    //"debugTokenExpression.boolean": "#4e94ce",
    //"debugTokenExpression.error": "#f48771",
    //"debugTokenExpression.name": "#c586c0",
    //"debugTokenExpression.number": "#b5cea8",
    //"debugTokenExpression.string": "#ce9178",
    //"debugTokenExpression.type": "#4a90e2",
    //"debugTokenExpression.value": "#cccccc99",
    //"debugView.exceptionLabelBackground": "#6c2022",
    //"debugView.exceptionLabelForeground": "#cccccc",
    //"debugView.stateLabelBackground": "#88888844",
    //"debugView.stateLabelForeground": "#cccccc",
    //"debugView.valueChangedHighlight": "#569cd6",
    //"diffEditor.diagonalFill": "#cccccc33",
    //"diffEditor.insertedLineBackground": "#9bb95533",
    //"diffEditor.insertedTextBackground": "#9ccc2c33",
    //"diffEditor.move.border": "#8b8b8b9c",
    //"diffEditor.moveActive.border": "#ffa500",
    //"diffEditor.removedLineBackground": "#ff000033",
    //"diffEditor.removedTextBackground": "#ff000033",
    //"diffEditor.unchangedCodeBackground": "#74747429",
    //"diffEditor.unchangedRegionBackground": "#181818",
    //"diffEditor.unchangedRegionForeground": "#cccccc",
    //"diffEditor.unchangedRegionShadow": "#000000",
    //"disabledForeground": "#cccccc80",
    //"editor.compositionBorder": "#ffffff",
    //"editor.findMatchHighlightBackground": "#ea5c0055",
    //"editor.findRangeHighlightBackground": "#3a3d4166",
    //"editor.focusedStackFrameHighlightBackground": "#7abd7a4d",
    //"editor.foldBackground": "#264f784d",
    //"editor.foldPlaceholderForeground": "#808080",
    //"editor.hoverHighlightBackground": "#264f7840",
    //"editor.inlineValuesBackground": "#ffc80033",
    //"editor.inlineValuesForeground": "#ffffff80",
    //"editor.lineHighlightBorder": "#282828",
    //"editor.linkedEditingBackground": "#ff00004d",
    //"editor.placeholder.foreground": "#ffffff56",
    //"editor.rangeHighlightBackground": "#ffffff0b",
    //"editor.selectionBackground": "#264f78",
    //"editor.snippetFinalTabstopHighlightBorder": "#525252",
    //"editor.snippetTabstopHighlightBackground": "#7c7c7c4d",
    //"editor.stackFrameHighlightBackground": "#ffff0033",
    //"editor.symbolHighlightBackground": "#ea5c0055",
    //"editor.wordHighlightBackground": "#575757b8",
    //"editor.wordHighlightStrongBackground": "#004972b8",
    //"editor.wordHighlightTextBackground": "#575757b8",
    //"editorActionList.background": "#202020",
    //"editorActionList.focusBackground": "#04395e",
    //"editorActionList.focusForeground": "#ffffff",
    //"editorActionList.foreground": "#cccccc",
    //"editorBracketHighlight.foreground1": "#ffd700",
    //"editorBracketHighlight.foreground2": "#da70d6",
    //"editorBracketHighlight.foreground3": "#179fff",
    //"editorBracketHighlight.foreground4": "#00000000",
    //"editorBracketHighlight.foreground5": "#00000000",
    //"editorBracketHighlight.foreground6": "#00000000",
    //"editorBracketHighlight.unexpectedBracket.foreground": "#ff1212cc",
    //"editorBracketMatch.background": "#0064001a",
    //"editorBracketMatch.border": "#888888",
    //"editorBracketPairGuide.activeBackground1": "#00000000",
    //"editorBracketPairGuide.activeBackground2": "#00000000",
    //"editorBracketPairGuide.activeBackground3": "#00000000",
    //"editorBracketPairGuide.activeBackground4": "#00000000",
    //"editorBracketPairGuide.activeBackground5": "#00000000",
    //"editorBracketPairGuide.activeBackground6": "#00000000",
    //"editorBracketPairGuide.background1": "#00000000",
    //"editorBracketPairGuide.background2": "#00000000",
    //"editorBracketPairGuide.background3": "#00000000",
    //"editorBracketPairGuide.background4": "#00000000",
    //"editorBracketPairGuide.background5": "#00000000",
    //"editorBracketPairGuide.background6": "#00000000",
    //"editorCodeLens.foreground": "#999999",
    //"editorCommentsWidget.rangeActiveBackground": "#0078d41a",
    //"editorCommentsWidget.rangeBackground": "#0078d41a",
    //"editorCommentsWidget.replyInputBackground": "#252526",
    //"editorCommentsWidget.resolvedBorder": "#cccccc80",
    //"editorCommentsWidget.unresolvedBorder": "#0078d4",
    //"editorCursor.foreground": "#aeafad",
    //"editorError.foreground": "#f14c4c",
    //"editorGhostText.foreground": "#ffffff56",
    //"editorGroup.dropBackground": "#53595d80",
    //"editorGroup.dropIntoPromptBackground": "#202020",
    //"editorGroup.dropIntoPromptForeground": "#cccccc",
    //"editorGroupHeader.noTabsBackground": "#1f1f1f",
    //"editorGutter.addedSecondaryBackground": "#175021",
    //"editorGutter.background": "#1f1f1f",
    //"editorGutter.commentGlyphForeground": "#cccccc",
    //"editorGutter.commentRangeForeground": "#37373d",
    //"editorGutter.commentUnresolvedGlyphForeground": "#cccccc",
    //"editorGutter.deletedSecondaryBackground": "#b91007",
    //"editorGutter.foldingControlForeground": "#cccccc",
    //"editorGutter.itemBackground": "#37373d",
    //"editorGutter.itemGlyphForeground": "#cccccc",
    //"editorGutter.modifiedSecondaryBackground": "#003c6a",
    //"editorHint.foreground": "#eeeeeeb3",
    //"editorHoverWidget.background": "#202020",
    //"editorHoverWidget.border": "#454545",
    //"editorHoverWidget.foreground": "#cccccc",
    //"editorHoverWidget.highlightForeground": "#2aaaff",
    //"editorHoverWidget.statusBarBackground": "#262626",
    //"editorIndentGuide.activeBackground2": "#00000000",
    //"editorIndentGuide.activeBackground3": "#00000000",
    //"editorIndentGuide.activeBackground4": "#00000000",
    //"editorIndentGuide.activeBackground5": "#00000000",
    //"editorIndentGuide.activeBackground6": "#00000000",
    //"editorIndentGuide.background2": "#00000000",
    //"editorIndentGuide.background3": "#00000000",
    //"editorIndentGuide.background4": "#00000000",
    //"editorIndentGuide.background5": "#00000000",
    //"editorIndentGuide.background6": "#00000000",
    //"editorInfo.foreground": "#3794ff",
    //"editorInlayHint.background": "#6161611a",
    //"editorInlayHint.foreground": "#969696",
    //"editorInlayHint.parameterBackground": "#6161611a",
    //"editorInlayHint.parameterForeground": "#969696",
    //"editorInlayHint.typeBackground": "#6161611a",
    //"editorInlayHint.typeForeground": "#969696",
    //"editorLightBulb.foreground": "#ffcc00",
    //"editorLightBulbAi.foreground": "#ffcc00",
    //"editorLightBulbAutoFix.foreground": "#75beff",
    //"editorLink.activeForeground": "#4e94ce",
    //"editorMarkerNavigation.background": "#1f1f1f",
    //"editorMarkerNavigationError.background": "#f14c4c",
    //"editorMarkerNavigationError.headerBackground": "#f14c4c1a",
    //"editorMarkerNavigationInfo.background": "#3794ff",
    //"editorMarkerNavigationInfo.headerBackground": "#3794ff1a",
    //"editorMarkerNavigationWarning.background": "#cca700",
    //"editorMarkerNavigationWarning.headerBackground": "#cca7001a",
    //"editorMinimap.inlineChatInserted": "#9ccc2c1f",
    //"editorMultiCursor.primary.foreground": "#aeafad",
    //"editorMultiCursor.secondary.foreground": "#aeafad",
    //"editorOverviewRuler.addedForeground": "#2ea04399",
    //"editorOverviewRuler.bracketMatchForeground": "#a0a0a0",
    //"editorOverviewRuler.commentForeground": "#37373d",
    //"editorOverviewRuler.commentUnresolvedForeground": "#37373d",
    //"editorOverviewRuler.commonContentForeground": "#60606066",
    //"editorOverviewRuler.currentContentForeground": "#40c8ae80",
    //"editorOverviewRuler.deletedForeground": "#f8514999",
    //"editorOverviewRuler.errorForeground": "#ff1212b3",
    //"editorOverviewRuler.findMatchForeground": "#d186167e",
    //"editorOverviewRuler.incomingContentForeground": "#40a6ff80",
    //"editorOverviewRuler.infoForeground": "#3794ff",
    //"editorOverviewRuler.inlineChatInserted": "#9ccc2c1f",
    //"editorOverviewRuler.inlineChatRemoved": "#ff00001f",
    //"editorOverviewRuler.modifiedForeground": "#0078d499",
    //"editorOverviewRuler.rangeHighlightForeground": "#007acc99",
    //"editorOverviewRuler.selectionHighlightForeground": "#a0a0a0cc",
    //"editorOverviewRuler.warningForeground": "#cca700",
    //"editorOverviewRuler.wordHighlightForeground": "#a0a0a0cc",
    //"editorOverviewRuler.wordHighlightStrongForeground": "#c0a0c0cc",
    //"editorOverviewRuler.wordHighlightTextForeground": "#a0a0a0cc",
    //"editorPane.background": "#1f1f1f",
    //"editorRuler.foreground": "#5a5a5a",
    //"editorStickyScroll.background": "#1f1f1f",
    //"editorStickyScroll.shadow": "#000000",
    //"editorStickyScrollGutter.background": "#1f1f1f",
    //"editorStickyScrollHover.background": "#2a2d2e",
    //"editorSuggestWidget.background": "#202020",
    //"editorSuggestWidget.border": "#454545",
    //"editorSuggestWidget.focusHighlightForeground": "#2aaaff",
    //"editorSuggestWidget.foreground": "#cccccc",
    //"editorSuggestWidget.highlightForeground": "#2aaaff",
    //"editorSuggestWidget.selectedBackground": "#04395e",
    //"editorSuggestWidget.selectedForeground": "#ffffff",
    //"editorSuggestWidget.selectedIconForeground": "#ffffff",
    //"editorSuggestWidgetStatus.foreground": "#cccccc80",
    //"editorUnicodeHighlight.border": "#cca700",
    //"editorUnnecessaryCode.opacity": "#000000aa",
    //"editorWarning.foreground": "#cca700",
    //"editorWatermark.foreground": "#cccccc99",
    //"editorWhitespace.foreground": "#e3e4e229",
    //"editorWidget.border": "#454545",
    //"editorWidget.foreground": "#cccccc",
    //"extensionBadge.remoteBackground": "#0078d4",
    //"extensionBadge.remoteForeground": "#ffffff",
    //"extensionButton.background": "#0078d4",
    //"extensionButton.foreground": "#ffffff",
    //"extensionButton.hoverBackground": "#026ec1",
    //"extensionButton.prominentBackground": "#0078d4",
    //"extensionButton.prominentForeground": "#ffffff",
    //"extensionButton.prominentHoverBackground": "#026ec1",
    //"extensionButton.separator": "#ffffff66",
    //"extensionIcon.preReleaseForeground": "#1d9271",
    //"extensionIcon.privateForeground": "#ffffff60",
    //"extensionIcon.sponsorForeground": "#d758b3",
    //"extensionIcon.starForeground": "#ff8e00",
    //"extensionIcon.verifiedForeground": "#4daafc",
    //"gauge.background": "#007acc",
    //"gauge.errorBackground": "#be1100",
    //"gauge.errorForeground": "#be11004d",
    //"gauge.foreground": "#007acc4d",
    //"gauge.warningBackground": "#b89500",
    //"gauge.warningForeground": "#b895004d",
    //"git.blame.editorDecorationForeground": "#969696",
    //"gitDecoration.addedResourceForeground": "#81b88b",
    //"gitDecoration.conflictingResourceForeground": "#e4676b",
    //"gitDecoration.deletedResourceForeground": "#c74e39",
    //"gitDecoration.ignoredResourceForeground": "#8c8c8c",
    //"gitDecoration.modifiedResourceForeground": "#e2c08d",
    //"gitDecoration.renamedResourceForeground": "#73c991",
    //"gitDecoration.stageDeletedResourceForeground": "#c74e39",
    //"gitDecoration.stageModifiedResourceForeground": "#e2c08d",
    //"gitDecoration.submoduleResourceForeground": "#8db9e2",
    //"gitDecoration.untrackedResourceForeground": "#73c991",
    //"inlineChat.background": "#202020",
    //"inlineChat.border": "#454545",
    //"inlineChat.foreground": "#cccccc",
    //"inlineChat.shadow": "#0000005c",
    //"inlineChatDiff.inserted": "#9ccc2c1a",
    //"inlineChatDiff.removed": "#ff00001a",
    //"inlineChatInput.background": "#313131",
    //"inlineChatInput.border": "#454545",
    //"inlineChatInput.focusBorder": "#0078d4",
    //"inlineChatInput.placeholderForeground": "#989898",
    //"inlineEdit.gutterIndicator.background": "#18181880",
    //"inlineEdit.gutterIndicator.primaryBackground": "#0078d466",
    //"inlineEdit.gutterIndicator.primaryBorder": "#0078d4",
    //"inlineEdit.gutterIndicator.primaryForeground": "#ffffff",
    //"inlineEdit.gutterIndicator.secondaryBackground": "#313131",
    //"inlineEdit.gutterIndicator.secondaryBorder": "#313131",
    //"inlineEdit.gutterIndicator.secondaryForeground": "#cccccc",
    //"inlineEdit.gutterIndicator.successfulBackground": "#0078d4",
    //"inlineEdit.gutterIndicator.successfulBorder": "#0078d4",
    //"inlineEdit.gutterIndicator.successfulForeground": "#ffffff",
    //"inlineEdit.modifiedBackground": "#9ccc2c0f",
    //"inlineEdit.modifiedBorder": "#9ccc2c33",
    //"inlineEdit.modifiedChangedLineBackground": "#9bb95524",
    //"inlineEdit.modifiedChangedTextBackground": "#9ccc2c24",
    //"inlineEdit.originalBackground": "#ff00000a",
    //"inlineEdit.originalBorder": "#ff000033",
    //"inlineEdit.originalChangedLineBackground": "#ff000029",
    //"inlineEdit.originalChangedTextBackground": "#ff000029",
    //"inlineEdit.tabWillAcceptModifiedBorder": "#9ccc2c33",
    //"inlineEdit.tabWillAcceptOriginalBorder": "#ff000033",
    //"inputOption.activeForeground": "#ffffff",
    //"inputOption.hoverBackground": "#5a5d5e80",
    //"inputValidation.errorBackground": "#5a1d1d",
    //"inputValidation.errorBorder": "#be1100",
    //"inputValidation.infoBackground": "#063b49",
    //"inputValidation.infoBorder": "#007acc",
    //"inputValidation.warningBackground": "#352a05",
    //"inputValidation.warningBorder": "#b89500",
    //"interactive.activeCodeBorder": "#007acc",
    //"interactive.inactiveCodeBorder": "#37373d",
    //"keybindingLabel.background": "#8080802b",
    //"keybindingLabel.border": "#33333399",
    //"keybindingLabel.bottomBorder": "#44444499",
    //"keybindingTable.headerBackground": "#cccccc0a",
    //"keybindingTable.rowsBackground": "#cccccc0a",
    //"list.activeSelectionBackground": "#04395e",
    //"list.activeSelectionForeground": "#ffffff",
    //"list.deemphasizedForeground": "#8c8c8c",
    //"list.dropBetweenBackground": "#cccccc",
    //"list.errorForeground": "#f88070",
    //"list.filterMatchBackground": "#ea5c0055",
    //"list.focusHighlightForeground": "#2aaaff",
    //"list.focusOutline": "#0078d4",
    //"list.highlightForeground": "#2aaaff",
    //"list.hoverBackground": "#2a2d2e",
    //"list.inactiveSelectionBackground": "#37373d",
    //"list.invalidItemForeground": "#b89500",
    //"list.warningForeground": "#cca700",
    //"listFilterWidget.background": "#202020",
    //"listFilterWidget.noMatchesOutline": "#be1100",
    //"listFilterWidget.outline": "#00000000",
    //"listFilterWidget.shadow": "#0000005c",
    //"menu.selectionForeground": "#ffffff",
    //"menubar.selectionBackground": "#5a5d5e50",
    //"menubar.selectionForeground": "#cccccc",
    //"merge.commonContentBackground": "#60606029",
    //"merge.commonHeaderBackground": "#60606066",
    //"merge.currentContentBackground": "#40c8ae33",
    //"merge.currentHeaderBackground": "#40c8ae80",
    //"merge.incomingContentBackground": "#40a6ff33",
    //"merge.incomingHeaderBackground": "#40a6ff80",
    //"mergeEditor.change.background": "#9bb95533",
    //"mergeEditor.change.word.background": "#9ccc2c33",
    //"mergeEditor.changeBase.background": "#4b1818",
    //"mergeEditor.changeBase.word.background": "#6f1313",
    //"mergeEditor.conflict.handled.minimapOverViewRuler": "#adaca8ee",
    //"mergeEditor.conflict.handledFocused.border": "#c1c1c1cc",
    //"mergeEditor.conflict.handledUnfocused.border": "#86868649",
    //"mergeEditor.conflict.input1.background": "#40c8ae33",
    //"mergeEditor.conflict.input2.background": "#40a6ff33",
    //"mergeEditor.conflict.unhandled.minimapOverViewRuler": "#fcba03",
    //"mergeEditor.conflict.unhandledFocused.border": "#ffa600",
    //"mergeEditor.conflict.unhandledUnfocused.border": "#ffa6007a",
    //"mergeEditor.conflictingLines.background": "#ffea0047",
    //"minimap.chatEditHighlight": "#1f1f1f99",
    //"minimap.errorHighlight": "#ff1212b3",
    //"minimap.findMatchHighlight": "#d18616",
    //"minimap.foregroundOpacity": "#000000",
    //"minimap.infoHighlight": "#3794ff",
    //"minimap.selectionHighlight": "#264f78",
    //"minimap.selectionOccurrenceHighlight": "#676767",
    //"minimap.warningHighlight": "#cca700",
    //"minimapGutter.addedBackground": "#2ea043",
    //"minimapGutter.deletedBackground": "#f85149",
    //"minimapGutter.modifiedBackground": "#0078d4",
    //"minimapSlider.activeBackground": "#bfbfbf33",
    //"minimapSlider.background": "#79797933",
    //"minimapSlider.hoverBackground": "#64646459",
    //"multiDiffEditor.background": "#1f1f1f",
    //"multiDiffEditor.border": "#2b2b2b",
    //"multiDiffEditor.headerBackground": "#262626",
    //"notebook.cellBorderColor": "#37373d",
    //"notebook.cellEditorBackground": "#181818",
    //"notebook.cellInsertionIndicator": "#0078d4",
    //"notebook.cellStatusBarItemHoverBackground": "#ffffff26",
    //"notebook.cellToolbarSeparator": "#80808059",
    //"notebook.editorBackground": "#1f1f1f",
    //"notebook.focusedCellBorder": "#0078d4",
    //"notebook.focusedEditorBorder": "#0078d4",
    //"notebook.inactiveFocusedCellBorder": "#37373d",
    //"notebook.selectedCellBackground": "#37373d",
    //"notebook.selectedCellBorder": "#37373d",
    //"notebook.symbolHighlightBackground": "#ffffff0b",
    //"notebookEditorOverviewRuler.runningCellForeground": "#89d185",
    //"notebookScrollbarSlider.activeBackground": "#bfbfbf66",
    //"notebookScrollbarSlider.background": "#79797966",
    //"notebookScrollbarSlider.hoverBackground": "#646464b3",
    //"notebookStatusErrorIcon.foreground": "#f85149",
    //"notebookStatusRunningIcon.foreground": "#cccccc",
    //"notebookStatusSuccessIcon.foreground": "#89d185",
    //"notificationCenter.border": "#313131",
    //"notificationLink.foreground": "#4daafc",
    //"notificationToast.border": "#313131",
    //"notificationsErrorIcon.foreground": "#f14c4c",
    //"notificationsInfoIcon.foreground": "#3794ff",
    //"notificationsWarningIcon.foreground": "#cca700",
    //"panel.dropBorder": "#cccccc",
    //"panelSection.border": "#2b2b2b",
    //"panelSection.dropBackground": "#53595d80",
    //"panelSectionHeader.background": "#80808033",
    //"panelStickyScroll.background": "#181818",
    //"panelStickyScroll.shadow": "#000000",
    //"panelTitleBadge.background": "#0078d4",
    //"panelTitleBadge.foreground": "#ffffff",
    //"peekView.border": "#3794ff",
    //"peekViewEditorGutter.background": "#1f1f1f",
    //"peekViewEditorStickyScroll.background": "#1f1f1f",
    //"peekViewEditorStickyScrollGutter.background": "#1f1f1f",
    //"peekViewResult.fileForeground": "#ffffff",
    //"peekViewResult.lineForeground": "#bbbbbb",
    //"peekViewResult.selectionBackground": "#3399ff33",
    //"peekViewResult.selectionForeground": "#ffffff",
    //"peekViewTitle.background": "#252526",
    //"peekViewTitleDescription.foreground": "#ccccccb3",
    //"peekViewTitleLabel.foreground": "#ffffff",
    //"pickerGroup.foreground": "#3794ff",
    //"problemsErrorIcon.foreground": "#f14c4c",
    //"problemsInfoIcon.foreground": "#3794ff",
    //"problemsWarningIcon.foreground": "#cca700",
    //"profileBadge.background": "#4d4d4d",
    //"profileBadge.foreground": "#ffffff",
    //"profiles.sashBorder": "#2b2b2b",
    //"quickInputList.focusBackground": "#04395e",
    //"quickInputList.focusForeground": "#ffffff",
    //"quickInputList.focusIconForeground": "#ffffff",
    //"quickInputTitle.background": "#ffffff1b",
    //"radio.activeBackground": "#2489db82",
    //"radio.activeBorder": "#2488db",
    //"radio.activeForeground": "#ffffff",
    //"radio.inactiveBorder": "#ffffff33",
    //"radio.inactiveHoverBackground": "#5a5d5e80",
    //"sash.hoverBorder": "#0078d4",
    //"scmGraph.foreground1": "#ffb000",
    //"scmGraph.foreground2": "#dc267f",
    //"scmGraph.foreground3": "#994f00",
    //"scmGraph.foreground4": "#40b0a6",
    //"scmGraph.foreground5": "#b66dff",
    //"scmGraph.historyItemBaseRefColor": "#ea5c00",
    //"scmGraph.historyItemHoverAdditionsForeground": "#81b88b",
    //"scmGraph.historyItemHoverDefaultLabelBackground": "#616161",
    //"scmGraph.historyItemHoverDefaultLabelForeground": "#cccccc",
    //"scmGraph.historyItemHoverDeletionsForeground": "#c74e39",
    //"scmGraph.historyItemHoverLabelForeground": "#ffffff",
    //"scmGraph.historyItemRefColor": "#3794ff",
    //"scmGraph.historyItemRemoteRefColor": "#b180d7",
    //"scrollbar.shadow": "#000000",
    //"scrollbarSlider.activeBackground": "#bfbfbf66",
    //"scrollbarSlider.background": "#79797966",
    //"scrollbarSlider.hoverBackground": "#646464b3",
    //"search.resultsInfoForeground": "#cccccca6",
    //"searchEditor.findMatchBackground": "#ea5c0038",
    //"searchEditor.textInputBorder": "#3c3c3c",
    //"settings.checkboxBackground": "#313131",
    //"settings.checkboxBorder": "#3c3c3c",
    //"settings.checkboxForeground": "#cccccc",
    //"settings.dropdownForeground": "#cccccc",
    //"settings.dropdownListBorder": "#454545",
    //"settings.focusedRowBackground": "#2a2d2e99",
    //"settings.focusedRowBorder": "#0078d4",
    //"settings.headerBorder": "#2b2b2b",
    //"settings.numberInputBackground": "#313131",
    //"settings.numberInputBorder": "#3c3c3c",
    //"settings.numberInputForeground": "#cccccc",
    //"settings.rowHoverBackground": "#2a2d2e4d",
    //"settings.sashBorder": "#2b2b2b",
    //"settings.settingsHeaderHoverForeground": "#ffffffb3",
    //"settings.textInputBackground": "#313131",
    //"settings.textInputBorder": "#3c3c3c",
    //"settings.textInputForeground": "#cccccc",
    //"sideBar.dropBackground": "#53595d80",
    //"sideBarActivityBarTop.border": "#2b2b2b",
    //"sideBarStickyScroll.background": "#181818",
    //"sideBarStickyScroll.shadow": "#000000",
    //"sideBarTitle.background": "#181818",
    //"sideBySideEditor.horizontalBorder": "#ffffff17",
    //"sideBySideEditor.verticalBorder": "#ffffff17",
    //"simpleFindWidget.sashBorder": "#454545",
    //"statusBar.debuggingBorder": "#2b2b2b",
    //"statusBar.noFolderBorder": "#2b2b2b",
    //"statusBar.noFolderForeground": "#cccccc",
    //"statusBarItem.activeBackground": "#ffffff2e",
    //"statusBarItem.compactHoverBackground": "#ffffff33",
    //"statusBarItem.errorBackground": "#b91007",
    //"statusBarItem.errorForeground": "#ffffff",
    //"statusBarItem.errorHoverBackground": "#ffffff1f",
    //"statusBarItem.errorHoverForeground": "#cccccc",
    //"statusBarItem.hoverBackground": "#ffffff1f",
    //"statusBarItem.hoverForeground": "#cccccc",
    //"statusBarItem.offlineBackground": "#6c1717",
    //"statusBarItem.offlineForeground": "#ffffff",
    //"statusBarItem.offlineHoverBackground": "#ffffff1f",
    //"statusBarItem.offlineHoverForeground": "#cccccc",
    //"statusBarItem.prominentForeground": "#cccccc",
    //"statusBarItem.prominentHoverBackground": "#ffffff1f",
    //"statusBarItem.prominentHoverForeground": "#cccccc",
    //"statusBarItem.remoteHoverBackground": "#ffffff1f",
    //"statusBarItem.remoteHoverForeground": "#cccccc",
    //"statusBarItem.warningBackground": "#7a6400",
    //"statusBarItem.warningForeground": "#ffffff",
    //"statusBarItem.warningHoverBackground": "#ffffff1f",
    //"statusBarItem.warningHoverForeground": "#cccccc",
    //"symbolIcon.arrayForeground": "#cccccc",
    //"symbolIcon.booleanForeground": "#cccccc",
    //"symbolIcon.classForeground": "#ee9d28",
    //"symbolIcon.colorForeground": "#cccccc",
    //"symbolIcon.constantForeground": "#cccccc",
    //"symbolIcon.constructorForeground": "#b180d7",
    //"symbolIcon.enumeratorForeground": "#ee9d28",
    //"symbolIcon.enumeratorMemberForeground": "#75beff",
    //"symbolIcon.eventForeground": "#ee9d28",
    //"symbolIcon.fieldForeground": "#75beff",
    //"symbolIcon.fileForeground": "#cccccc",
    //"symbolIcon.folderForeground": "#cccccc",
    //"symbolIcon.functionForeground": "#b180d7",
    //"symbolIcon.interfaceForeground": "#75beff",
    //"symbolIcon.keyForeground": "#cccccc",
    //"symbolIcon.keywordForeground": "#cccccc",
    //"symbolIcon.methodForeground": "#b180d7",
    //"symbolIcon.moduleForeground": "#cccccc",
    //"symbolIcon.namespaceForeground": "#cccccc",
    //"symbolIcon.nullForeground": "#cccccc",
    //"symbolIcon.numberForeground": "#cccccc",
    //"symbolIcon.objectForeground": "#cccccc",
    //"symbolIcon.operatorForeground": "#cccccc",
    //"symbolIcon.packageForeground": "#cccccc",
    //"symbolIcon.propertyForeground": "#cccccc",
    //"symbolIcon.referenceForeground": "#cccccc",
    //"symbolIcon.snippetForeground": "#cccccc",
    //"symbolIcon.stringForeground": "#cccccc",
    //"symbolIcon.structForeground": "#cccccc",
    //"symbolIcon.textForeground": "#cccccc",
    //"symbolIcon.typeParameterForeground": "#cccccc",
    //"symbolIcon.unitForeground": "#cccccc",
    //"symbolIcon.variableForeground": "#75beff",
    //"tab.activeModifiedBorder": "#3399cc",
    //"tab.dragAndDropBorder": "#ffffff",
    //"tab.inactiveModifiedBorder": "#3399cc80",
    //"tab.unfocusedActiveBackground": "#1f1f1f",
    //"tab.unfocusedActiveForeground": "#ffffff80",
    //"tab.unfocusedActiveModifiedBorder": "#3399cc80",
    //"tab.unfocusedInactiveBackground": "#181818",
    //"tab.unfocusedInactiveForeground": "#9d9d9d80",
    //"tab.unfocusedInactiveModifiedBorder": "#3399cc40",
    //"terminal.ansiBlack": "#000000",
    //"terminal.ansiBlue": "#2472c8",
    //"terminal.ansiBrightBlack": "#666666",
    //"terminal.ansiBrightBlue": "#3b8eea",
    //"terminal.ansiBrightCyan": "#29b8db",
    //"terminal.ansiBrightGreen": "#23d18b",
    //"terminal.ansiBrightMagenta": "#d670d6",
    //"terminal.ansiBrightRed": "#f14c4c",
    //"terminal.ansiBrightWhite": "#e5e5e5",
    //"terminal.ansiBrightYellow": "#f5f543",
    //"terminal.ansiCyan": "#11a8cd",
    //"terminal.ansiGreen": "#0dbc79",
    //"terminal.ansiMagenta": "#bc3fbc",
    //"terminal.ansiRed": "#cd3131",
    //"terminal.ansiWhite": "#e5e5e5",
    //"terminal.ansiYellow": "#e5e510",
    //"terminal.border": "#2b2b2b",
    //"terminal.dropBackground": "#53595d80",
    //"terminal.findMatchBackground": "#9e6a03",
    //"terminal.findMatchHighlightBackground": "#ea5c0055",
    //"terminal.hoverHighlightBackground": "#264f7820",
    //"terminal.initialHintForeground": "#ffffff56",
    //"terminal.selectionBackground": "#264f78",
    //"terminalCommandDecoration.defaultBackground": "#ffffff40",
    //"terminalCommandDecoration.errorBackground": "#f14c4c",
    //"terminalCommandDecoration.successBackground": "#1b81a8",
    //"terminalCommandGuide.foreground": "#37373d",
    //"terminalOverviewRuler.border": "#010409",
    //"terminalOverviewRuler.cursorForeground": "#a0a0a0cc",
    //"terminalOverviewRuler.findMatchForeground": "#d186167e",
    //"terminalStickyScrollHover.background": "#2a2d2e",
    //"terminalSymbolIcon.aliasForeground": "#b180d7",
    //"terminalSymbolIcon.argumentForeground": "#75beff",
    //"terminalSymbolIcon.fileForeground": "#cccccc",
    //"terminalSymbolIcon.flagForeground": "#ee9d28",
    //"terminalSymbolIcon.folderForeground": "#cccccc",
    //"terminalSymbolIcon.methodForeground": "#b180d7",
    //"terminalSymbolIcon.optionForeground": "#ee9d28",
    //"terminalSymbolIcon.optionValueForeground": "#75beff",
    //"terminalSymbolIcon.symbolicLinkFileForeground": "#cccccc",
    //"terminalSymbolIcon.symbolicLinkFolderForeground": "#cccccc",
    //"testing.coverCountBadgeBackground": "#616161",
    //"testing.coverCountBadgeForeground": "#f8f8f8",
    //"testing.coveredBackground": "#9ccc2c33",
    //"testing.coveredBorder": "#9ccc2c26",
    //"testing.coveredGutterBackground": "#9ccc2c1f",
    //"testing.iconErrored": "#f14c4c",
    //"testing.iconErrored.retired": "#f14c4cb3",
    //"testing.iconFailed": "#f14c4c",
    //"testing.iconFailed.retired": "#f14c4cb3",
    //"testing.iconPassed": "#73c991",
    //"testing.iconPassed.retired": "#73c991b3",
    //"testing.iconQueued": "#cca700",
    //"testing.iconQueued.retired": "#cca700b3",
    //"testing.iconSkipped": "#848484",
    //"testing.iconSkipped.retired": "#848484b3",
    //"testing.iconUnset": "#848484",
    //"testing.iconUnset.retired": "#848484b3",
    //"testing.message.error.badgeBackground": "#f14c4c",
    //"testing.message.error.badgeBorder": "#f14c4c",
    //"testing.message.error.badgeForeground": "#000000",
    //"testing.message.info.decorationForeground": "#cccccc80",
    //"testing.messagePeekBorder": "#3794ff",
    //"testing.messagePeekHeaderBackground": "#3794ff1a",
    //"testing.peekBorder": "#f14c4c",
    //"testing.peekHeaderBackground": "#f14c4c1a",
    //"testing.runAction": "#73c991",
    //"testing.uncoveredBackground": "#ff000033",
    //"testing.uncoveredBorder": "#ff000026",
    //"testing.uncoveredBranchBackground": "#781212",
    //"testing.uncoveredGutterBackground": "#ff00004d",
    //"toolbar.activeBackground": "#63666750",
    //"toolbar.hoverBackground": "#5a5d5e50",
    //"tree.inactiveIndentGuidesStroke": "#58585866",
    //"tree.indentGuidesStroke": "#585858",
    //"tree.tableColumnsBorder": "#cccccc20",
    //"tree.tableOddRowsBackground": "#cccccc0a",
    //"walkThrough.embeddedEditorBackground": "#00000066",
    //"walkthrough.stepTitle.foreground": "#ffffff",
    //"welcomePage.progress.background": "#313131",
    //"welcomePage.tileBorder": "#ffffff1a",
    //"welcomePage.tileHoverBackground": "#262626",
    //"widget.shadow": "#0000005c",
    //"activityBar.activeBackground": null,
    //"activityBar.activeFocusBorder": null,
    //"activityBarTop.activeBackground": null,
    //"activityBarTop.background": null,
    //"contrastActiveBorder": null,
    //"contrastBorder": null,
    //"debugToolBar.border": null,
    //"diffEditor.border": null,
    //"diffEditor.insertedTextBorder": null,
    //"diffEditor.removedTextBorder": null,
    //"diffEditorGutter.insertedLineBackground": null,
    //"diffEditorGutter.removedLineBackground": null,
    //"diffEditorOverview.insertedForeground": null,
    //"diffEditorOverview.removedForeground": null,
    //"editor.findMatchBorder": null,
    //"editor.findMatchForeground": null,
    //"editor.findMatchHighlightBorder": null,
    //"editor.findMatchHighlightForeground": null,
    //"editor.findRangeHighlightBorder": null,
    //"editor.lineHighlightBackground": null,
    //"editor.rangeHighlightBorder": null,
    //"editor.selectionForeground": null,
    //"editor.selectionHighlightBorder": null,
    //"editor.snippetFinalTabstopHighlightBackground": null,
    //"editor.snippetTabstopHighlightBorder": null,
    //"editor.symbolHighlightBorder": null,
    //"editor.wordHighlightBorder": null,
    //"editor.wordHighlightStrongBorder": null,
    //"editor.wordHighlightTextBorder": null,
    //"editorCursor.background": null,
    //"editorError.background": null,
    //"editorError.border": null,
    //"editorGhostText.background": null,
    //"editorGhostText.border": null,
    //"editorGroup.dropIntoPromptBorder": null,
    //"editorGroup.emptyBackground": null,
    //"editorGroup.focusedEmptyBorder": null,
    //"editorGroupHeader.border": null,
    //"editorHint.border": null,
    //"editorInfo.background": null,
    //"editorInfo.border": null,
    //"editorLineNumber.dimmedForeground": null,
    //"editorMultiCursor.primary.background": null,
    //"editorMultiCursor.secondary.background": null,
    //"editorOverviewRuler.background": null,
    //"editorStickyScroll.border": null,
    //"editorUnicodeHighlight.background": null,
    //"editorUnnecessaryCode.border": null,
    //"editorWarning.background": null,
    //"editorWarning.border": null,
    //"editorWidget.resizeBorder": null,
    //"gauge.border": null,
    //"inputValidation.errorForeground": null,
    //"inputValidation.infoForeground": null,
    //"inputValidation.warningForeground": null,
    //"list.filterMatchBorder": null,
    //"list.focusAndSelectionOutline": null,
    //"list.focusBackground": null,
    //"list.focusForeground": null,
    //"list.hoverForeground": null,
    //"list.inactiveFocusBackground": null,
    //"list.inactiveFocusOutline": null,
    //"list.inactiveSelectionForeground": null,
    //"list.inactiveSelectionIconForeground": null,
    //"menu.selectionBorder": null,
    //"menubar.selectionBorder": null,
    //"merge.border": null,
    //"minimap.background": null,
    //"notebook.cellHoverBackground": null,
    //"notebook.focusedCellBackground": null,
    //"notebook.inactiveSelectedCellBorder": null,
    //"notebook.outputContainerBackgroundColor": null,
    //"notebook.outputContainerBorderColor": null,
    //"outputView.background": null,
    //"outputViewStickyScroll.background": null,
    //"panelSectionHeader.border": null,
    //"panelSectionHeader.foreground": null,
    //"panelStickyScroll.border": null,
    //"panelTitle.border": null,
    //"peekViewEditor.matchHighlightBorder": null,
    //"radio.inactiveBackground": null,
    //"radio.inactiveForeground": null,
    //"searchEditor.findMatchBorder": null,
    //"selection.background": null,
    //"sideBarStickyScroll.border": null,
    //"sideBarTitle.border": null,
    //"tab.hoverBorder": null,
    //"tab.hoverForeground": null,
    //"tab.unfocusedHoverBorder": null,
    //"tab.unfocusedHoverForeground": null,
    //"terminal.background": null,
    //"terminal.findMatchBorder": null,
    //"terminal.findMatchHighlightBorder": null,
    //"terminal.selectionForeground": null,
    //"terminalCursor.background": null,
    //"terminalCursor.foreground": null,
    //"terminalStickyScroll.background": null,
    //"terminalStickyScroll.border": null,
    //"terminalSymbolIcon.inlineSuggestionForeground": null,
    //"testing.message.error.lineBackground": null,
    //"testing.message.info.lineBackground": null,
    //"toolbar.hoverOutline": null,
    //"welcomePage.background": null,
    //"window.activeBorder": null,
    //"window.inactiveBorder": null
  },
  "tokenColors": [
    {
      "scope": [
        "meta.embedded",
        "source.groovy.embedded",
        "string meta.image.inline.markdown",
        "variable.legacy.builtin.python"
      ],
      "settings": {
        "foreground": "#D4D4D4"
      }
    },
    {
      "scope": "emphasis",
      "settings": {
        "fontStyle": "italic"
      }
    },
    {
      "scope": "strong",
      "settings": {
        "fontStyle": "bold"
      }
    },
    {
      "scope": "header",
      "settings": {
        "foreground": "#000080"
      }
    },
    {
      "scope": "comment",
      "settings": {
        "foreground": "#6A9955"
      }
    },
    {
      "scope": "constant.language",
      "settings": {
        "foreground": "#569CD6"
      }
    },
    {
      "scope": [
        "constant.numeric",
        "variable.other.enummember",
        "keyword.operator.plus.exponent",
        "keyword.operator.minus.exponent"
      ],
      "settings": {
        "foreground": "#B5CEA8"
      }
    },
    {
      "scope": "constant.regexp",
      "settings": {
        "foreground": "#646695"
      }
    },
    {
      "scope": "entity.name.tag",
      "settings": {
        "foreground": "#569CD6"
      }
    },
    {
      "scope": ["entity.name.tag.css", "entity.name.tag.less"],
      "settings": {
        "foreground": "#D7BA7D"
      }
    },
    {
      "scope": "entity.other.attribute-name",
      "settings": {
        "foreground": "#9CDCFE"
      }
    },
    {
      "scope": [
        "entity.other.attribute-name.class.css",
        "source.css entity.other.attribute-name.class",
        "entity.other.attribute-name.id.css",
        "entity.other.attribute-name.parent-selector.css",
        "entity.other.attribute-name.parent.less",
        "source.css entity.other.attribute-name.pseudo-class",
        "entity.other.attribute-name.pseudo-element.css",
        "source.css.less entity.other.attribute-name.id",
        "entity.other.attribute-name.scss"
      ],
      "settings": {
        "foreground": "#D7BA7D"
      }
    },
    {
      "scope": "invalid",
      "settings": {
        "foreground": "#F44747"
      }
    },
    {
      "scope": "markup.underline",
      "settings": {
        "fontStyle": "underline"
      }
    },
    {
      "scope": "markup.bold",
      "settings": {
        "foreground": "#569CD6",
        "fontStyle": "bold"
      }
    },
    {
      "scope": "markup.heading",
      "settings": {
        "foreground": "#569CD6",
        "fontStyle": "bold"
      }
    },
    {
      "scope": "markup.italic",
      "settings": {
        "fontStyle": "italic"
      }
    },
    {
      "scope": "markup.strikethrough",
      "settings": {
        "fontStyle": "strikethrough"
      }
    },
    {
      "scope": "markup.inserted",
      "settings": {
        "foreground": "#B5CEA8"
      }
    },
    {
      "scope": "markup.deleted",
      "settings": {
        "foreground": "#CE9178"
      }
    },
    {
      "scope": "markup.changed",
      "settings": {
        "foreground": "#569CD6"
      }
    },
    {
      "scope": "punctuation.definition.quote.begin.markdown",
      "settings": {
        "foreground": "#6A9955"
      }
    },
    {
      "scope": "punctuation.definition.list.begin.markdown",
      "settings": {
        "foreground": "#6796E6"
      }
    },
    {
      "scope": "markup.inline.raw",
      "settings": {
        "foreground": "#CE9178"
      }
    },
    {
      "scope": "punctuation.definition.tag",
      "settings": {
        "foreground": "#808080"
      }
    },
    {
      "scope": ["meta.preprocessor", "entity.name.function.preprocessor"],
      "settings": {
        "foreground": "#569CD6"
      }
    },
    {
      "scope": "meta.preprocessor.string",
      "settings": {
        "foreground": "#CE9178"
      }
    },
    {
      "scope": "meta.preprocessor.numeric",
      "settings": {
        "foreground": "#B5CEA8"
      }
    },
    {
      "scope": "meta.structure.dictionary.key.python",
      "settings": {
        "foreground": "#9CDCFE"
      }
    },
    {
      "scope": "meta.diff.header",
      "settings": {
        "foreground": "#569CD6"
      }
    },
    {
      "scope": "storage",
      "settings": {
        "foreground": "#569CD6"
      }
    },
    {
      "scope": "storage.type",
      "settings": {
        "foreground": "#569CD6"
      }
    },
    {
      "scope": ["storage.modifier", "keyword.operator.noexcept"],
      "settings": {
        "foreground": "#569CD6"
      }
    },
    {
      "scope": ["string", "meta.embedded.assembly"],
      "settings": {
        "foreground": "#CE9178"
      }
    },
    {
      "scope": "string.tag",
      "settings": {
        "foreground": "#CE9178"
      }
    },
    {
      "scope": "string.value",
      "settings": {
        "foreground": "#CE9178"
      }
    },
    {
      "scope": "string.regexp",
      "settings": {
        "foreground": "#D16969"
      }
    },
    {
      "scope": [
        "punctuation.definition.template-expression.begin",
        "punctuation.definition.template-expression.end",
        "punctuation.section.embedded"
      ],
      "settings": {
        "foreground": "#569CD6"
      }
    },
    {
      "scope": ["meta.template.expression"],
      "settings": {
        "foreground": "#D4D4D4"
      }
    },
    {
      "scope": [
        "support.type.vendored.property-name",
        "support.type.property-name",
        "source.css variable",
        "source.coffee.embedded"
      ],
      "settings": {
        "foreground": "#9CDCFE"
      }
    },
    {
      "scope": "keyword",
      "settings": {
        "foreground": "#569CD6"
      }
    },
    {
      "scope": "keyword.control",
      "settings": {
        "foreground": "#569CD6"
      }
    },
    {
      "scope": "keyword.operator",
      "settings": {
        "foreground": "#D4D4D4"
      }
    },
    {
      "scope": [
        "keyword.operator.new",
        "keyword.operator.expression",
        "keyword.operator.cast",
        "keyword.operator.sizeof",
        "keyword.operator.alignof",
        "keyword.operator.typeid",
        "keyword.operator.alignas",
        "keyword.operator.instanceof",
        "keyword.operator.logical.python",
        "keyword.operator.wordlike"
      ],
      "settings": {
        "foreground": "#569CD6"
      }
    },
    {
      "scope": "keyword.other.unit",
      "settings": {
        "foreground": "#B5CEA8"
      }
    },
    {
      "scope": ["punctuation.section.embedded.begin.php", "punctuation.section.embedded.end.php"],
      "settings": {
        "foreground": "#569CD6"
      }
    },
    {
      "scope": "support.function.git-rebase",
      "settings": {
        "foreground": "#9CDCFE"
      }
    },
    {
      "scope": "constant.sha.git-rebase",
      "settings": {
        "foreground": "#B5CEA8"
      }
    },
    {
      "scope": [
        "storage.modifier.import.java",
        "variable.language.wildcard.java",
        "storage.modifier.package.java"
      ],
      "settings": {
        "foreground": "#D4D4D4"
      }
    },
    {
      "scope": "variable.language",
      "settings": {
        "foreground": "#569CD6"
      }
    },
    {
      "scope": [
        "entity.name.function",
        "support.function",
        "support.constant.handlebars",
        "source.powershell variable.other.member",
        "entity.name.operator.custom-literal"
      ],
      "settings": {
        "foreground": "#DCDCAA"
      }
    },
    {
      "scope": [
        "support.class",
        "support.type",
        "entity.name.type",
        "entity.name.namespace",
        "entity.other.attribute",
        "entity.name.scope-resolution",
        "entity.name.class",
        "storage.type.numeric.go",
        "storage.type.byte.go",
        "storage.type.boolean.go",
        "storage.type.string.go",
        "storage.type.uintptr.go",
        "storage.type.error.go",
        "storage.type.rune.go",
        "storage.type.cs",
        "storage.type.generic.cs",
        "storage.type.modifier.cs",
        "storage.type.variable.cs",
        "storage.type.annotation.java",
        "storage.type.generic.java",
        "storage.type.java",
        "storage.type.object.array.java",
        "storage.type.primitive.array.java",
        "storage.type.primitive.java",
        "storage.type.token.java",
        "storage.type.groovy",
        "storage.type.annotation.groovy",
        "storage.type.parameters.groovy",
        "storage.type.generic.groovy",
        "storage.type.object.array.groovy",
        "storage.type.primitive.array.groovy",
        "storage.type.primitive.groovy"
      ],
      "settings": {
        "foreground": "#4EC9B0"
      }
    },
    {
      "scope": [
        "meta.type.cast.expr",
        "meta.type.new.expr",
        "support.constant.math",
        "support.constant.dom",
        "support.constant.json",
        "entity.other.inherited-class",
        "punctuation.separator.namespace.ruby"
      ],
      "settings": {
        "foreground": "#4EC9B0"
      }
    },
    {
      "scope": [
        "keyword.control",
        "source.cpp keyword.operator.new",
        "keyword.operator.delete",
        "keyword.other.using",
        "keyword.other.directive.using",
        "keyword.other.operator",
        "entity.name.operator"
      ],
      "settings": {
        "foreground": "#C586C0"
      }
    },
    {
      "scope": [
        "variable",
        "meta.definition.variable.name",
        "support.variable",
        "entity.name.variable",
        "constant.other.placeholder"
      ],
      "settings": {
        "foreground": "#9CDCFE"
      }
    },
    {
      "scope": ["variable.other.constant", "variable.other.enummember"],
      "settings": {
        "foreground": "#4FC1FF"
      }
    },
    {
      "scope": ["meta.object-literal.key"],
      "settings": {
        "foreground": "#9CDCFE"
      }
    },
    {
      "scope": [
        "support.constant.property-value",
        "support.constant.font-name",
        "support.constant.media-type",
        "support.constant.media",
        "constant.other.color.rgb-value",
        "constant.other.rgb-value",
        "support.constant.color"
      ],
      "settings": {
        "foreground": "#CE9178"
      }
    },
    {
      "scope": [
        "punctuation.definition.group.regexp",
        "punctuation.definition.group.assertion.regexp",
        "punctuation.definition.character-class.regexp",
        "punctuation.character.set.begin.regexp",
        "punctuation.character.set.end.regexp",
        "keyword.operator.negation.regexp",
        "support.other.parenthesis.regexp"
      ],
      "settings": {
        "foreground": "#CE9178"
      }
    },
    {
      "scope": [
        "constant.character.character-class.regexp",
        "constant.other.character-class.set.regexp",
        "constant.other.character-class.regexp",
        "constant.character.set.regexp"
      ],
      "settings": {
        "foreground": "#D16969"
      }
    },
    {
      "scope": ["keyword.operator.or.regexp", "keyword.control.anchor.regexp"],
      "settings": {
        "foreground": "#DCDCAA"
      }
    },
    {
      "scope": "keyword.operator.quantifier.regexp",
      "settings": {
        "foreground": "#D7BA7D"
      }
    },
    {
      "scope": ["constant.character", "constant.other.option"],
      "settings": {
        "foreground": "#569CD6"
      }
    },
    {
      "scope": "constant.character.escape",
      "settings": {
        "foreground": "#D7BA7D"
      }
    },
    {
      "scope": "entity.name.label",
      "settings": {
        "foreground": "#C8C8C8"
      }
    },
    {
      "scope": "token.info-token",
      "settings": {
        "foreground": "#6796E6"
      }
    },
    {
      "scope": "token.warn-token",
      "settings": {
        "foreground": "#CD9731"
      }
    },
    {
      "scope": "token.error-token",
      "settings": {
        "foreground": "#F44747"
      }
    },
    {
      "scope": "token.debug-token",
      "settings": {
        "foreground": "#B267E6"
      }
    }
  ]
}
