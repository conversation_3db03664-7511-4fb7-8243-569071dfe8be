# Angular First Project Tutorial

## 🚀 Project Overview

This is a beginner-friendly Angular project that demonstrates fundamental Angular concepts including components, data binding, event handling, and modern Angular features like signals. The project uses Angular 20.2.0 with standalone components and the latest Angular features.

## 📁 Project Structure

```
first-ng-app/
├── src/
│   ├── app/
│   │   ├── components/
│   │   │   ├── header/          # Navigation header component
│   │   │   ├── greeting/        # Reusable greeting component
│   │   │   ├── counter/         # Interactive counter component
│   │   │   ├── todo-item/       # Individual todo item component
│   │   │   └── about/           # About page component
│   │   ├── directives/
│   │   │   └── highlight-completed-todo.ts  # Custom directive for styling
│   │   ├── services/
│   │   │   └── todos.ts         # HTTP service for API calls
│   │   ├── model/
│   │   │   └── todo.type.ts     # TypeScript type definitions
│   │   ├── home/                # Home page component
│   │   ├── todos/               # Todos page component with HTTP integration
│   │   ├── app.ts               # Root component with routing
│   │   ├── app.config.ts        # Application configuration with HTTP client
│   │   └── app.routes.ts        # Lazy-loaded routing configuration
│   ├── main.ts                  # Application bootstrap
│   ├── index.html               # Main HTML file
│   └── styles.scss              # Global styles
├── angular.json                 # Angular CLI configuration
├── package.json                 # Dependencies and scripts
└── tsconfig.json               # TypeScript configuration
```

## 🧩 Components Breakdown

### 1. Root Component (`app.ts`) - Now with Routing!

The main application component now uses Angular Router for navigation:

```typescript
@Component({
  selector: "app-root",
  imports: [RouterOutlet, Header],
  template: `
    <app-header />
    <main>
      <router-outlet />
    </main>
  `,
  styles: [
    /* inline styles */
  ],
})
export class App {}
```

**Key Concepts:**

- **Router Integration**: Uses `<router-outlet>` for dynamic component loading
- **Simplified Structure**: Components are now loaded via routing
- **Lazy Loading**: Components are loaded on-demand for better performance
- **Navigation Architecture**: Clean separation between navigation and content

### 2. Routing Configuration (`app.routes.ts`)

Modern Angular routing with lazy-loaded components:

```typescript
export const routes: Routes = [
  {
    path: "",
    pathMatch: "full",
    loadComponent: () => import("./home/<USER>").then((c) => c.Home),
  },
  {
    path: "todos",
    loadComponent: () => import("./todos/todos").then((c) => c.Todos),
  },
  {
    path: "about",
    loadComponent: () =>
      import("./components/about/about").then((c) => c.About),
  },
];
```

**Key Concepts:**

- **Lazy Loading**: Components are loaded only when needed
- **Dynamic Imports**: Uses ES6 import() for code splitting
- **Route Configuration**: Maps URLs to components
- **Performance Optimization**: Smaller initial bundle size

### 3. Header Component (`components/header/`)

A navigation header with dynamic title using Angular signals:

**TypeScript (`header.ts`):**

```typescript
@Component({
  selector: "app-header",
  imports: [],
  templateUrl: "./header.html",
  styleUrls: ["./header.scss"],
})
export class Header {
  title = signal("Title spp"); // Angular signal for reactive data
}
```

**Template (`header.html`):**

```html
<header>
  <span>{{title()}}</span>
  <!-- Signal interpolation -->
  <nav>
    <ul>
      <li><a href="/">Home</a></li>
      <li><a href="/about">About</a></li>
      <li><a href="/contact">Contact</a></li>
    </ul>
  </nav>
</header>
```

**Key Concepts:**

- **Signals**: Modern reactive primitive for state management
- **String Interpolation**: `{{}}` syntax for displaying data
- **External Templates**: Separate HTML file for template
- **SCSS Styling**: Nested CSS with variables and mixins support

### 3. Greeting Component (`components/greeting/`)

A reusable component that accepts input data:

**TypeScript (`greeting.ts`):**

```typescript
@Component({
  selector: "app-greeting",
  imports: [],
  templateUrl: "./greeting.html",
  styleUrl: "./greeting.scss",
})
export class Greeting {
  message = input("Default messgae"); // Input signal with default value
  inputValue = input(""); // Additional input signal
}
```

**Template (`greeting.html`):**

```html
<p>greeting works!</p>
<p>{{message()}}</p>
<!-- Display input message -->
<p>{{inputValue()}}</p>
<!-- Display dynamic input value -->
```

**Key Concepts:**

- **Input Signals**: Modern way to receive data from parent components
- **Component Reusability**: Can be used in multiple places
- **Default Values**: Fallback values for inputs
- **Multiple Inputs**: Components can accept multiple input properties

### 4. Counter Component (`components/counter/`)

An interactive component demonstrating state management and user interactions:

**TypeScript (`counter.ts`):**

```typescript
@Component({
  selector: "app-counter",
  imports: [],
  templateUrl: "./counter.html",
  styleUrl: "./counter.scss",
})
export class Counter {
  value = signal(0); // Signal to track counter value

  increment() {
    this.value.update((val) => val + 1); // Increment using update method
  }

  decrement() {
    this.value.update((val) => val - 1); // Decrement using update method
  }

  reset() {
    this.value.set(0); // Reset using set method
  }
}
```

**Template (`counter.html`):**

```html
<p>counter works!</p>
<p>Value: {{value()}}</p>

<div class="btn-group">
  <button (click)="increment()">Increment</button>
  <button (click)="decrement()">Decrement</button>
  <button (click)="reset()">Reset</button>
</div>
```

**Styles (`counter.scss`):**

```scss
.btn-group {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background-color: #f1f1f1;

  button {
    width: content-box;
    height: 50px;
    font-size: 1.5rem;
  }
}
```

**Key Concepts:**

- **Signal State Management**: Using signals for reactive state
- **Signal Update Methods**: `.set()` for direct assignment, `.update()` for transformations
- **Click Event Handling**: Multiple button interactions
- **Component Styling**: SCSS with flexbox layout
- **Interactive UI**: Real-time updates based on user actions

### 5. Todos Component (`todos/`) - HTTP Integration & Advanced Features

A sophisticated component demonstrating HTTP services, lifecycle hooks, and modern Angular patterns:

**TypeScript (`todos.ts`):**

```typescript
@Component({
  selector: "app-todos",
  imports: [TodoItem],
  templateUrl: "./todos.html",
  styleUrl: "./todos.scss",
})
export class Todos implements OnInit {
  todoService = inject(TodosService);
  todoItems = signal<Array<Todo>>([]);

  ngOnInit() {
    this.todoService
      .getTodosFromApi()
      .pipe(
        catchError((err) => {
          console.log(err);
          return [];
        })
      )
      .subscribe((todos) => {
        this.todoItems.set(todos);
      });
  }
}
```

**Template (`todos.html`):**

```html
<h3>Todos list</h3>
@if(todoItems().length === 0){
<p>Loading...</p>
}
<ul class="todos">
  @for (todo of todoItems(); track todo.id) {
  <app-todo-item [todo]="todo" />
  }
</ul>
```

**Key Concepts:**

- **HTTP Service Integration**: Fetches data from external API
- **Lifecycle Hooks**: Uses `OnInit` for initialization logic
- **Dependency Injection**: Injects services using `inject()` function
- **Error Handling**: Uses RxJS `catchError` operator
- **Control Flow**: Modern `@if` and `@for` syntax
- **Loading States**: Conditional rendering based on data state
- **Track By**: Optimizes list rendering with `track` function

### 6. Todo Service (`services/todos.ts`)

HTTP service for API communication:

```typescript
@Injectable({
  providedIn: "root",
})
export class Todos {
  http = inject(HttpClient);

  getTodosFromApi() {
    return this.http.get<Todo[]>(
      "https://jsonplaceholder.typicode.com/todos?_limit=10"
    );
  }
}
```

**Key Concepts:**

- **Injectable Service**: Provides data to components
- **HTTP Client**: Makes API requests
- **Generic Types**: Type-safe HTTP responses
- **Root Provider**: Available throughout the application

### 7. Todo Item Component (`components/todo-item/`)

Individual todo item with custom directive and output events:

**TypeScript (`todo-item.ts`):**

```typescript
@Component({
  selector: "app-todo-item",
  imports: [HighlightCompletedTodo],
  templateUrl: "./todo-item.html",
  styleUrl: "./todo-item.scss",
})
export class TodoItem {
  todo = input.required<Todo>();
  todoToggled = output<Todo>();

  todoClicked() {
    this.todoToggled.emit(this.todo());
  }
}
```

**Template (`todo-item.html`):**

```html
<li
  appHighlightCompletedTodo
  [isCompleted]="todo().completed"
  class="todos_item"
>
  <input
    id="todo__{{todo().id}}"
    type="checkbox"
    [checked]="todo().completed"
    (change)="this.todoClicked()"
  />
  <label for="todo__{{todo().id}}">{{todo().title}}</label>
</li>
```

**Key Concepts:**

- **Required Inputs**: Using `input.required<T>()` for mandatory props
- **Output Events**: Emitting events to parent components
- **Custom Directives**: Using custom attribute directives
- **Accessibility**: Proper label-input association with `id` and `for`

### 8. Custom Directive (`directives/highlight-completed-todo.ts`)

Advanced directive using effects for dynamic styling:

```typescript
@Directive({
  selector: "[appHighlightCompletedTodo]",
})
export class HighlightCompletedTodo {
  isCompleted = input(false);
  el = inject(ElementRef);

  styleEffect = effect(() => {
    if (this.isCompleted()) {
      this.el.nativeElement.style.textDecoration = "line-through";
      this.el.nativeElement.style.color = "red";
      this.el.nativeElement.style.opacity = "0.5";
    } else {
      this.el.nativeElement.style.textDecoration = "none";
      this.el.nativeElement.style.color = "black";
      this.el.nativeElement.style.opacity = "1";
    }
  });
}
```

**Key Concepts:**

- **Custom Directives**: Reusable DOM manipulation logic
- **Effects**: Reactive side effects with `effect()`
- **Element Injection**: Direct DOM access with `ElementRef`
- **Dynamic Styling**: Conditional CSS application
- **Signal-Driven**: Reactive updates based on input changes

### 9. TypeScript Models (`model/todo.type.ts`)

Type-safe data structures:

```typescript
export type Todo = {
  userId: number;
  completed: boolean;
  title: string;
  id: number;
};
```

**Key Concepts:**

- **Type Safety**: Compile-time type checking
- **Data Modeling**: Clear data structure definitions
- **Reusability**: Shared types across components and services

### 10. Home Component (`home/`)

Demonstrates event handling and two-way data flow:

**TypeScript (`home.ts`):**

```typescript
@Component({
  selector: "app-home",
  imports: [Greeting, Counter], // Import both child components
  templateUrl: "./home.html",
  styleUrls: ["./home.scss"],
})
export class Home {
  message = signal("hello from greetinging home");
  inputValue = signal("");

  keyupHandler(event: KeyboardEvent) {
    this.message.set(event.key); // Update signal value
  }
}
```

**Template (`home.html`):**

```html
<h2>
  This is my h2 heading
  <app-greeting [message]="message()" />
  <!-- Property binding -->
</h2>
<input placeholder="type here" type="text" (keyup)="keyupHandler($event)" />
<!-- Event binding -->
<app-counter />
<!-- Counter component -->
```

**Key Concepts:**

- **Component Composition**: Using multiple child components (Greeting and Counter)
- **Event Binding**: `(keyup)` syntax for handling events
- **Property Binding**: `[message]` syntax for passing data to child components
- **Signal Updates**: Using `.set()` method to update signal values
- **Event Objects**: Accessing event data with `$event`
- **Template Organization**: Structuring HTML with multiple components

### 11. About Component (`components/about/`)

Simple component demonstrating basic string interpolation:

**TypeScript (`about.ts`):**

```typescript
@Component({
  selector: "app-about",
  imports: [],
  templateUrl: "./about.html",
  styleUrl: "./about.scss",
})
export class About {
  title = "About"; // Simple property
}
```

**Template (`about.html`):**

```html
<p>about works! {{ title}}</p>
<!-- String interpolation -->
```

## 🎨 Styling Architecture

### Global Styles (`styles.scss`)

```scss
html,
body {
  margin: 0;
  padding: 0;
}
* {
  box-sizing: border-box;
  font-family: "Roboto", sans-serif;
  outline: none;
}
```

### Component-Specific Styles

Each component has its own SCSS file for encapsulated styling:

**Header Styles (`header.scss`):**

```scss
header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background-color: #f1f1f1;
  nav {
    ul {
      display: flex;
      list-style: none;
      li {
        margin-right: 1rem;
        a {
          text-decoration: none;
          color: #333;
        }
      }
    }
  }
}
```

**Key Concepts:**

- **CSS Encapsulation**: Styles are scoped to components
- **SCSS Nesting**: Hierarchical CSS structure
- **Flexbox Layout**: Modern CSS layout system

## 🔧 Configuration Files

### Application Configuration (`app.config.ts`)

Modern Angular configuration with HTTP client setup:

```typescript
export const appConfig: ApplicationConfig = {
  providers: [
    provideBrowserGlobalErrorListeners(),
    provideZoneChangeDetection({ eventCoalescing: true }),
    provideRouter(routes),
    provideHttpClient(), // HTTP client for API calls
  ],
};
```

**Key Concepts:**

- **Standalone Configuration**: No NgModule required
- **HTTP Client Provider**: Enables HTTP services
- **Router Provider**: Enables routing functionality
- **Zone Change Detection**: Optimized change detection

### Angular Configuration (`angular.json`)

- Defines build configurations
- Sets up SCSS as the default style preprocessor
- Configures development and production builds

### Package Configuration (`package.json`)

- Lists Angular 20.2.0 dependencies
- Includes development scripts
- Prettier configuration for code formatting

### TypeScript Configuration (`tsconfig.json`)

- Enables strict type checking
- Configures module resolution
- Sets up Angular-specific compiler options

## 🚦 Getting Started

### Prerequisites

- Node.js (v18 or higher)
- npm or yarn package manager

### Installation & Running

```bash
# Install dependencies
npm install

# Start development server
ng serve
# or
npm start

# Build for production
ng build

# Run tests
ng test
```

### Development Server

- Runs on `http://localhost:4200/`
- Hot reload enabled for instant updates
- Automatic compilation on file changes

## 🎯 Key Learning Points

### 1. Modern Angular Features

- **Standalone Components**: No need for NgModules
- **Signals**: Reactive state management with `.set()` and `.update()` methods
- **Input/Output Signals**: Modern component communication
- **Effects**: Reactive side effects with `effect()`
- **Control Flow**: New `@if` and `@for` syntax
- **Lazy Loading**: Route-based code splitting

### 2. Data Binding & Communication

- **Interpolation**: `{{value}}` - Display data
- **Property Binding**: `[property]="value"` - Pass data to components
- **Event Binding**: `(event)="handler()"` - Handle user interactions
- **Output Events**: Component-to-parent communication
- **Custom Directives**: Reusable DOM manipulation

### 3. HTTP & Services

- **HTTP Client**: API communication with type safety
- **Injectable Services**: Dependency injection pattern
- **Error Handling**: RxJS operators for robust data fetching
- **Loading States**: Managing async data states
- **Service Injection**: Modern `inject()` function

### 4. Routing & Navigation

- **Lazy Loading**: Components loaded on-demand
- **Route Configuration**: URL-to-component mapping
- **Router Outlet**: Dynamic component rendering
- **Code Splitting**: Optimized bundle sizes

### 5. Advanced Patterns

- **Custom Directives**: Reusable DOM behavior
- **TypeScript Models**: Type-safe data structures
- **Lifecycle Hooks**: Component initialization logic
- **Effects**: Signal-driven side effects
- **Track Functions**: Optimized list rendering

## 🔄 Data Flow Examples

### 1. Input Event Flow (Greeting Component)

1. User types in the input field in `Home` component
2. `keyupHandler` method is triggered
3. The `message` signal is updated with the pressed key
4. The updated message is passed to the `Greeting` component via property binding
5. The `Greeting` component displays the new message
6. UI updates automatically due to Angular's change detection

### 2. Counter Interaction Flow

1. User clicks increment/decrement/reset button in `Counter` component
2. Corresponding method (`increment()`, `decrement()`, or `reset()`) is triggered
3. The `value` signal is updated using `.update()` or `.set()` methods
4. The counter display updates immediately showing the new value
5. All changes are reactive and automatic

### 3. HTTP Data Flow (Todos Component)

1. User navigates to `/todos` route
2. `Todos` component is lazy-loaded and initialized
3. `ngOnInit` lifecycle hook triggers
4. `TodosService.getTodosFromApi()` makes HTTP request to JSONPlaceholder API
5. RxJS `catchError` operator handles potential errors
6. Successful response updates `todoItems` signal with fetched data
7. Template re-renders with new data using `@for` control flow
8. Loading state disappears, todos list appears

### 4. Custom Directive Flow (Todo Highlighting)

1. `TodoItem` component renders with `appHighlightCompletedTodo` directive
2. `isCompleted` input signal is passed to directive
3. `effect()` function monitors signal changes
4. When `isCompleted` changes, effect triggers
5. DOM styles are updated directly via `ElementRef`
6. Visual feedback (strikethrough, opacity, color) applied instantly

### 5. Output Event Flow (Todo Item Interaction)

1. User clicks checkbox in `TodoItem` component
2. `(change)` event triggers `todoClicked()` method
3. `todoToggled.emit()` sends todo data to parent
4. Parent component receives event (if listening)
5. Event bubbles up the component tree for handling

## 🎨 Styling Strategy

- **Global Reset**: Normalize browser defaults
- **Component Scoping**: Each component has isolated styles
- **SCSS Features**: Nesting, variables, and mixins
- **Responsive Design**: Flexbox for layout management

## 📚 Next Steps

To extend this project, consider:

1. **Enhanced Counter Features**: Add min/max limits, step values, or persistence
2. **Form Validation**: Add input validation to the greeting component
3. **Routing**: Navigate between different views/pages
4. **HTTP Services**: Fetch data from APIs
5. **Advanced State Management**: Share state between components
6. **Unit Testing**: Test all component methods and interactions
7. **Styling Enhancements**: Add animations and responsive design
8. **Local Storage**: Persist counter value between sessions

## 🎉 Project Summary

This Angular project successfully demonstrates:

### 🏗️ **Architecture & Structure**

✅ **7+ Well-Structured Components**: Header, Greeting, Counter, Todos, TodoItem, About, and Home
✅ **Modern Angular Patterns**: Standalone components with signals and effects
✅ **Lazy Loading**: Route-based code splitting for optimal performance
✅ **Clean Architecture**: Services, models, directives, and components properly separated

### 🔄 **Data Management & Communication**

✅ **HTTP Integration**: Real API calls with error handling
✅ **Signal-Based State**: Reactive state management with automatic UI updates
✅ **Component Communication**: Input/output signals and event emission
✅ **Type Safety**: TypeScript models and generic HTTP responses

### 🎨 **User Experience & Interaction**

✅ **Interactive Features**: Counter, todo list, and real-time input handling
✅ **Custom Directives**: Dynamic styling with effects
✅ **Loading States**: Proper async data handling
✅ **Accessibility**: Proper form labels and semantic HTML

### 🛠️ **Advanced Angular Features**

✅ **Routing System**: Multi-page navigation with lazy loading
✅ **Lifecycle Hooks**: Component initialization patterns
✅ **RxJS Integration**: Observable streams and error handling
✅ **Modern Control Flow**: `@if` and `@for` syntax
✅ **Effects**: Reactive side effects for DOM manipulation

### 💻 **Development Best Practices**

✅ **Professional Styling**: SCSS with component-scoped styles
✅ **TypeScript Integration**: Full type safety throughout
✅ **Service Architecture**: Injectable services with dependency injection
✅ **Error Handling**: Robust error management patterns

This project showcases **production-ready Angular patterns** and serves as an excellent foundation for building enterprise-level applications! 🚀
